﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\PropertyChanged.Fody.3.4.1\build\PropertyChanged.Fody.props" Condition="Exists('..\packages\PropertyChanged.Fody.3.4.1\build\PropertyChanged.Fody.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{E22A9798-55CB-4BC3-80ED-6DB1B87E28B3}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>F7_L12_Position_Detect</RootNamespace>
    <AssemblyName>F7_L12_Position_Detect</AssemblyName>
    <TargetFrameworkVersion>v4.7</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="CommonServiceLocator, Version=2.0.2.0, Culture=neutral, PublicKeyToken=489b6accfaf20ef0, processorArchitecture=MSIL">
      <HintPath>..\packages\CommonServiceLocator.2.0.2\lib\net47\CommonServiceLocator.dll</HintPath>
    </Reference>
    <Reference Include="GalaSoft.MvvmLight, Version=5.4.1.0, Culture=neutral, PublicKeyToken=e7570ab207bcb616, processorArchitecture=MSIL">
      <HintPath>..\packages\MvvmLightLibs.5.4.1.1\lib\net45\GalaSoft.MvvmLight.dll</HintPath>
    </Reference>
    <Reference Include="GalaSoft.MvvmLight.Extras, Version=5.4.1.0, Culture=neutral, PublicKeyToken=669f0b5e8f868abf, processorArchitecture=MSIL">
      <HintPath>..\packages\MvvmLightLibs.5.4.1.1\lib\net45\GalaSoft.MvvmLight.Extras.dll</HintPath>
    </Reference>
    <Reference Include="GalaSoft.MvvmLight.Platform, Version=5.4.1.0, Culture=neutral, PublicKeyToken=5f873c45e98af8a1, processorArchitecture=MSIL">
      <HintPath>..\packages\MvvmLightLibs.5.4.1.1\lib\net45\GalaSoft.MvvmLight.Platform.dll</HintPath>
    </Reference>
    <Reference Include="halcondotnet, Version=21.5.0.0, Culture=neutral, PublicKeyToken=4973bed59ddbf2b8, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\dll\halcondotnet.dll</HintPath>
    </Reference>
    <Reference Include="HslCommunication">
      <HintPath>dll\HslCommunication.dll</HintPath>
    </Reference>
    <Reference Include="Panuon.WPF, Version=1.0.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Panuon.WPF.1.0.3\lib\net462\Panuon.WPF.dll</HintPath>
    </Reference>
    <Reference Include="Panuon.WPF.UI, Version=1.1.17.3, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Panuon.WPF.UI.1.1.17.3\lib\net462\Panuon.WPF.UI.dll</HintPath>
    </Reference>
    <Reference Include="PropertyChanged, Version=3.4.1.0, Culture=neutral, PublicKeyToken=ee3ee20bcf148ddd, processorArchitecture=MSIL">
      <HintPath>..\packages\PropertyChanged.Fody.3.4.1\lib\net40\PropertyChanged.dll</HintPath>
    </Reference>
    <Reference Include="Scanner">
      <HintPath>dll\Scanner.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Interactivity, Version=4.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\MvvmLightLibs.5.4.1.1\lib\net45\System.Windows.Interactivity.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Compile Include="Control\Toolcontrol.xaml.cs">
      <DependentUpon>Toolcontrol.xaml</DependentUpon>
    </Compile>
    <Compile Include="convertor.cs" />
    <Compile Include="Hobject2Bitmap.cs" />
    <Compile Include="Log\Log.cs" />
    <Compile Include="Model\Record\Record.cs" />
    <Compile Include="Model\DataModel.cs" />
    <Compile Include="Model\Processmodel.cs" />
    <Compile Include="Model\RecordModel.cs" />
    <Compile Include="Model\SettingModel.cs" />
    <Compile Include="Model\Setting\ImageSaveSetting.cs" />
    <Compile Include="Model\Setting\TcpSetting.cs" />
    <Compile Include="Model\Tool\ToolModel.cs" />
    <Compile Include="SettingForm.xaml.cs">
      <DependentUpon>SettingForm.xaml</DependentUpon>
    </Compile>
    <Compile Include="ViewModel\MainViewModel.cs" />
    <Compile Include="ViewModel\ViewModelLocator.cs" />
    <Compile Include="View\InitPositionWindow.xaml.cs">
      <DependentUpon>InitPositionWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="View\Inputbox.xaml.cs">
      <DependentUpon>Inputbox.xaml</DependentUpon>
    </Compile>
    <Compile Include="View\InputPassword.xaml.cs">
      <DependentUpon>InputPassword.xaml</DependentUpon>
    </Compile>
    <Compile Include="View\ModelWindow.xaml.cs">
      <DependentUpon>ModelWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="View\newPrj.xaml.cs">
      <DependentUpon>newPrj.xaml</DependentUpon>
    </Compile>
    <Page Include="Control\Toolcontrol.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="MainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Page Include="SettingForm.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="View\InitPositionWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="View\Inputbox.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="View\InputPassword.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="View\ModelWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="View\newPrj.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Model\Process\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Camera\Camera.csproj">
      <Project>{671e5d0c-031d-49eb-872e-c4940d6c921d}</Project>
      <Name>Camera</Name>
    </ProjectReference>
    <ProjectReference Include="..\HF800\Honeywell.csproj">
      <Project>{f9c8e8a3-77c6-4667-a3eb-ad898bc97df2}</Project>
      <Name>Honeywell</Name>
    </ProjectReference>
    <ProjectReference Include="..\PositionDetect\PositionDetect.csproj">
      <Project>{ac2ca99b-7a0b-4f36-ae44-23105cca4332}</Project>
      <Name>PositionDetect</Name>
    </ProjectReference>
    <ProjectReference Include="..\QRCode\QRCode.csproj">
      <Project>{3b9b9569-4d43-476b-b6e9-0ab4448dd5ba}</Project>
      <Name>QRCode</Name>
    </ProjectReference>
    <ProjectReference Include="..\Sqlserver\Sqlserver.csproj">
      <Project>{f8216c62-2b91-459c-87f4-ed866540ef51}</Project>
      <Name>Sqlserver</Name>
    </ProjectReference>
    <ProjectReference Include="..\TcpClientHelper\TcpClientHelper.csproj">
      <Project>{5dbf2327-fc16-4f70-8aaf-a9438b35742d}</Project>
      <Name>TcpClientHelper</Name>
    </ProjectReference>
    <ProjectReference Include="..\TcpServerHelper\TcpServerHelper.csproj">
      <Project>{82f63f5a-7caf-4287-ad60-3d7d97a2a7dc}</Project>
      <Name>TcpServerHelper</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\PropertyChanged.Fody.3.4.1\build\PropertyChanged.Fody.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\PropertyChanged.Fody.3.4.1\build\PropertyChanged.Fody.props'))" />
    <Error Condition="!Exists('..\packages\Fody.6.6.2\build\Fody.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Fody.6.6.2\build\Fody.targets'))" />
  </Target>
  <Import Project="..\packages\Fody.6.6.2\build\Fody.targets" Condition="Exists('..\packages\Fody.6.6.2\build\Fody.targets')" />
</Project>