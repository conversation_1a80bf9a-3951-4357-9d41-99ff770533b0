﻿<UserControl x:Class="Camera.CameraControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:p="https://opensource.panuon.com/wpf-ui"
             xmlns:local="clr-namespace:Camera"
             xmlns:halcon="clr-namespace:HalconDotNet;assembly=halcondotnet"
             mc:Ignorable="d" 
             xmlns:data="clr-namespace:Camera"
             DataContextChanged="UserControl_DataContextChanged"
             d:DesignHeight="450" d:DesignWidth="800">
    <!--<UserControl.DataContext>
        <data:DATA />
    </UserControl.DataContext>-->
    <Grid>
        <Grid Margin="5">

            <Grid.ColumnDefinitions>
                <ColumnDefinition/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="auto"/>
                <RowDefinition/>
                <RowDefinition Height="auto"/>
                <RowDefinition Height="auto"/>
            </Grid.RowDefinitions>
            <halcon:HSmartWindowControlWPF  Name="HWindow" Grid.Row="1" />
            <!--<halcon:HWindowControlWPF Name="HWindow" Grid.Row="1"  />-->
            
            
            <DockPanel>
                <!--<Button Click="Button_Click" DockPanel.Dock="Right" Margin="10,2,0,2" Padding="10,0">测试图片</Button>-->
                <Label Background="Transparent" VerticalAlignment="Center">相机:</Label>
                <ComboBox p:ComboBoxHelper.CornerRadius="15" Height="30" Margin="2" Padding="15,0"  MinWidth="500"
                          VerticalContentAlignment="Center"
                      ItemsSource="{Binding  CameraModel.camera.cbDeviceList}"
                          SelectedIndex="{Binding CameraModel.camera.SelectedIndex,UpdateSourceTrigger=PropertyChanged}"
                      />
            </DockPanel>
            <Grid Grid.Column="1" Grid.RowSpan="2" Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="auto"/>
                    <RowDefinition Height="auto"/>
                    <RowDefinition Height="auto"/>
                    <RowDefinition Height="auto"/>
                    <RowDefinition Height="auto"/>
                </Grid.RowDefinitions>


            </Grid>

            <Grid Grid.Column="0" Grid.Row="2" Grid.ColumnSpan="2" Margin="0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="auto"/>
                    <ColumnDefinition/>
                    <ColumnDefinition Width="auto"/>
                    <ColumnDefinition Width="auto"/>
                </Grid.ColumnDefinitions>

                <GroupBox Header="初始化" Grid.Column="0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition/>
                            <ColumnDefinition/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition/>
                            <RowDefinition/>
                        </Grid.RowDefinitions>
                        <Button Margin="2" Grid.Row="0" Grid.Column="0" Grid.RowSpan="2" Name="bnEnum" Command="{Binding CameraModel.camera.DeviceListAcq_Command}" p:ButtonHelper.CornerRadius="10">查找设备</Button>
                        <Button Margin="2" Grid.Row="0" Grid.Column="1" Name="bnOpen" Command="{Binding CameraModel.camera.bnOpen_Click_Command}" IsEnabled="{Binding CameraModel.camera.bnOpenEnabled}">打开设备</Button>
                        <Button Margin="2" Grid.Row="1" Grid.Column="1" Name="bnClose" Command="{Binding CameraModel.camera.bnClose_Click_Command}" IsEnabled="{Binding CameraModel.camera.bnCloseEnabled}">关闭设备</Button>
                    </Grid>
                </GroupBox>
                <GroupBox Header="采集图像" Grid.Column="1">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition/>
                            <ColumnDefinition/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                        </Grid.RowDefinitions>
                        <RadioButton Grid.Row="0" Grid.Column="0" Margin="2" Name="bnContinuesMode" Checked="bnContinuesMode_Checked" IsChecked="{Binding CameraModel.camera.bnContinuesModeChecked,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding CameraModel.camera.bnContinuesModeEnabled}" >连续模式</RadioButton>
                        <RadioButton Grid.Row="0" Grid.Column="1" Margin="2" Name="bnTriggerMode" Checked="bnTriggerMode_Checked"  IsChecked="{Binding CameraModel.camera.bnTriggerModeChecked,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding CameraModel.camera.bnTriggerModeEnabled}">触发模式</RadioButton>
                        <Button Grid.Row="1" Grid.Column="0" Margin="2" Name="bnStartGrab" Command="{Binding CameraModel.camera.bnStartGrab_Click_Command}" IsEnabled="{Binding CameraModel.camera.bnStartGrabEnabled}">开始采集</Button>
                        <Button Grid.Row="1" Grid.Column="1" Margin="2" Name="bnStopGrab" Command="{Binding CameraModel.camera.bnStopGrab_Click_Command}" IsEnabled="{Binding CameraModel.camera.bnStopGrabEnabled}">停止采集</Button>
                        <CheckBox Grid.Row="2" Grid.Column="0" Margin="2" Name="cbSoftTrigger" Checked="cbSoftTrigger_Checked"  IsChecked="{Binding CameraModel.camera.cbSoftTriggerChecked}" IsEnabled="{Binding camera.cbSoftTriggerEnabled}">软触发</CheckBox>
                        <Button Grid.Row="2" Grid.Column="1" Margin="2" Name="bnTriggerExec" Command="{Binding CameraModel.camera.bnTriggerExec_Click_Command}" IsEnabled="{Binding CameraModel.camera.bnTriggerExecEnabled}" >软触发一次</Button>

                    </Grid>
                </GroupBox>
                <GroupBox Header="保存图片" Grid.Column="2" Visibility="Collapsed">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition/>
                            <ColumnDefinition/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition/>
                            <RowDefinition/>
                        </Grid.RowDefinitions>

                        <Button Grid.Row="0" Grid.Column="0" Margin="2" Name="bnSaveBmp" Command="{Binding CameraModel.camera.bnSaveBmp_Click_Command}" IsEnabled="{Binding  CameraModel.camera.bnSaveBmpEnabled}">保存BMP</Button>
                        <Button Grid.Row="0" Grid.Column="1" Margin="2" Name="bnSaveJpg" Command="{Binding CameraModel.camera.bnSaveJpg_Click_Command}" IsEnabled="{Binding CameraModel.camera.bnSaveJpgEnabled}">保存JPG</Button>
                        <Button Grid.Row="1" Grid.Column="0" Margin="2" Name="bnSaveTiff" Command="{Binding CameraModel.camera.bnSaveTiff_Click_Command}" IsEnabled="{Binding CameraModel.camera.bnSaveTiffEnabled}">保存TIFF</Button>
                        <Button Grid.Row="1" Grid.Column="1" Margin="2" Name="bnSavePng" Command="{Binding CameraModel.camera.bnSavePng_Click_Command}" IsEnabled="{Binding CameraModel.camera.bnSavePngEnabled}">保存PNG</Button>
                    </Grid>

                </GroupBox>
                <GroupBox Header="参数" Grid.Column="3">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="auto"/>
                            <ColumnDefinition/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                        </Grid.RowDefinitions>

                        <Label Grid.Row="0" Background="Transparent" Grid.Column="0" Margin="2">曝光</Label>
                        <!--<Label Grid.Row="1" Background="Transparent" Grid.Column="0" Margin="2">增益</Label>
                        <Label Grid.Row="2" Background="Transparent" Grid.Column="0" Margin="2">帧率</Label>-->

                        <TextBox Grid.Row="0" Grid.Column="1" Margin="2" IsEnabled="{Binding CameraModel.camera.tbExposureEnabled}" Text="{Binding CameraModel.camera.Exposure}"></TextBox>
                        <!--<TextBox Grid.Row="1" Grid.Column="1" Margin="2" IsEnabled="{Binding CameraModel.camera.tbGainEnabled}" Text="{Binding CameraModel.camera.Gain}"></TextBox>
                        <TextBox Grid.Row="2" Grid.Column="1" Margin="2" IsEnabled="{Binding CameraModel.camera.tbFrameRateEnabled}" Text="{Binding CameraModel.camera.FrameRate}"></TextBox>-->

                        <Grid  Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="3">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition/>
                                <ColumnDefinition/>
                            </Grid.ColumnDefinitions>
                            <Button Margin="2" Name="bnGetParam" Grid.Column="0" Padding="30,5" Command="{Binding CameraModel.camera.bnGetParam_Click_Command}" IsEnabled="{Binding CameraModel.camera.bnGetParamEnabled}">获取参数</Button>
                            <Button Margin="2" Name="bnSetParam" Grid.Column="1" Padding="30,5" Command="{Binding CameraModel.camera.bnSetParam_Click_Command}" IsEnabled="{Binding CameraModel.camera.bnSetParamEnabled}" Click="bnSetParam_Click">设置参数</Button>


                        </Grid>
                    </Grid>

                </GroupBox>

            </Grid>
            <Grid Grid.Row="3" Grid.ColumnSpan="2" HorizontalAlignment="Center" >
                <TextBlock Text="{Binding trig,StringFormat=PLC触发:{0}}" Margin="10,2" Background="{Binding trigbrush}" />
            </Grid>
        </Grid>
    </Grid>
</UserControl>
