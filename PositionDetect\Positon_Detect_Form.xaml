﻿<UserControl x:Class="PositionDetect.UserControl1"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:p="https://opensource.panuon.com/wpf-ui"
             xmlns:local="clr-namespace:PositionDetect"
             mc:Ignorable="d" 
             xmlns:halcon="clr-namespace:HalconDotNet;assembly=halcondotnet"
             xmlns:viewmodel="clr-namespace:PositionDetect.ViewModel"
             d:DesignHeight="450"
             d:DesignWidth="800"
             DataContextChanged="UserControl_DataContextChanged"
           >
    <!--<UserControl.DataContext>
        <viewmodel:PositionDetectViewModel x:Name="vmodel"/>
    </UserControl.DataContext>-->
<Grid Margin="5"   >
        <Grid.ColumnDefinitions>
            <ColumnDefinition/>
            <ColumnDefinition Width="auto"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto"/>
            <RowDefinition/>
            <RowDefinition Height="auto"/>
        </Grid.RowDefinitions>
        <halcon:HWindowControlWPF Name="HWindow" Grid.Row="0" Grid.RowSpan="2" />
        <!--<halcon:HSmartWindowControlWPF Name="HWindow" Grid.Row="0" Grid.RowSpan="2" />-->
        <!--<DockPanel>
            <Label Background="Transparent" VerticalAlignment="Center">相机:</Label>
            <ComboBox p:ComboBoxHelper.CornerRadius="15" Height="30" Margin="2" Padding="15,0" DropDownClosed="ComboBox_DropDownClosed"></ComboBox>
        </DockPanel>-->
        <StackPanel Grid.Row="0" Grid.Column="1" Grid.RowSpan="2">
            <GroupBox Header="特征选择">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="auto"/>
                        <RowDefinition Height="auto"/>
                        <RowDefinition/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition/>
                        <ColumnDefinition/>
                    </Grid.ColumnDefinitions>
                    <Button Grid.Row="0" Grid.Column="0" Margin="5" Padding="10" Grid.ColumnSpan="2" Content="{Binding  DATA.ROIModeStr}"  Click="Button_Click"/>
                    <Button Grid.Row="1" Grid.Column="0" Margin="5" Padding="10" Content="矩形选区" IsEnabled="{Binding DATA.ROImode}" Background="{Binding DATA.RectangleMmode}" Click="Button_Click_1"/>
                    <Button Grid.Row="1" Grid.Column="1" Margin="5" Padding="10" Content="圆形选区" IsEnabled="{Binding DATA.ROImode}" Background="{Binding DATA.CircleMode}" Click="Button_Click_2"/>
                    <ListBox Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" Margin="5" Padding="10" ItemsSource="{Binding DATA.Objects}" 
                             SelectedIndex="{Binding DATA.selectedindex}"
                             SelectionChanged="ListBox_SelectionChanged" KeyDown="ListBox_KeyDown" />
                </Grid>
            </GroupBox>

            <GroupBox Header="预览" >
                <Button Grid.Row="0" Grid.Column="0" Margin="5" Padding="10" Content="预览" IsEnabled="{Binding DATA.ROImode}" Click="Button_Click_5" />
            </GroupBox>
            <GroupBox Header="导出" >
                <Button Grid.Row="0" Grid.Column="0" Margin="5" Padding="10" Content="导出" IsEnabled="{Binding DATA.ROImode}" Click="Button_Click_3"/>
            </GroupBox>
        </StackPanel>

    </Grid>
</UserControl>
