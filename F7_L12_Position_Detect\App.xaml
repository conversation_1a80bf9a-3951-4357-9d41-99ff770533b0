<Application x:Class="F7_L12_Position_Detect.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:F7_L12_Position_Detect" 
             StartupUri="MainWindow.xaml" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             d1p1:Ignorable="d" 
             xmlns:d1p1="http://schemas.openxmlformats.org/markup-compatibility/2006">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Panuon.WPF.UI;component/Control.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <vm:ViewModelLocator x:Key="Locator" d:IsDataSource="True" xmlns:vm="clr-namespace:F7_L12_Position_Detect.ViewModel" />
            <local:Bool2OKNG_Converter               x:Key="Bool2OKNG_Converter"/>
            <local:Bool2OKNG_Converter_INV           x:Key="Bool2OKNG_Converter_INV"/>
            <local:Bool2SolidColor_Converter         x:Key="Bool2SolidColor_Converter"/>
            <local:Bool2SolidColor_Converter_INV     x:Key="Bool2SolidColor_Converter_INV"/>
            <local:Status2BackGround_Converter       x:Key="Status2BackGround_Converter"/>
            <local:Status2ForeGround_Converter       x:Key="Status2ForeGround_Converter"/>
            <local:Status2ZH_Converter               x:Key="Status2ZH_Converter"/>
            <local:Bool2Visible_Converter            x:Key="Bool2Visible_Converter"/>
            <local:Bool2Visible_Converter_INV        x:Key="Bool2Visible_Converter_INV"/>
            <local:BoolINV_Converter                 x:Key="BoolINV_Converter"/>
            <local:permission2lockorunlock_Converter x:Key="permission2lockorunlock_Converter"/>
            <local:TestMode2Visibility_Barcode       x:Key="TestMode2Visibility_Barcode"/>
            <local:TestMode2Visibility_Dimension     x:Key="TestMode2Visibility_Dimension"/>
            <local:TestMode2Visibility_ShapeMatch    x:Key="TestMode2Visibility_ShapeMatch"/>

        </ResourceDictionary>
    </Application.Resources>
</Application>