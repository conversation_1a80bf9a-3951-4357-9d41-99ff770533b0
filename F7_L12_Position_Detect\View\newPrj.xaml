﻿<p:WindowX x:Class="F7_L12_Position_Detect.View.newPrj"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:F7_L12_Position_Detect.View"
        xmlns:p="clr-namespace:Panuon.WPF.UI;assembly=Panuon.WPF.UI"
        mc:Ignorable="d"      
        p:WindowXCaption.Background="#666666"
        p:WindowXCaption.Height="45"  
        WindowStartupLocation="CenterScreen"
           p:WindowXCaption.Foreground="WhiteSmoke"
        Title="新建工程" Height="180" Width="400">
    <Grid Margin="5">

        <Grid.RowDefinitions>
            <RowDefinition Height="30"/>
            <RowDefinition Height="auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="auto"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="auto"/>
            <ColumnDefinition/>
        </Grid.ColumnDefinitions>
        <TextBlock Grid.Row="1" Text="工程名称" VerticalAlignment="Center"/>
        <!--<ComboBox x:Name="Tooltype_combobox" Grid.Row="1" Grid.Column="1" Height="30" p:ComboBoxHelper.CornerRadius="15" Padding="15,0" ItemsSource="{Binding Source={StaticResource Tooltype}}" />-->
        <TextBox x:Name="Prj_TextBox" Grid.Row="1" Grid.Column="1" Height="30"  Padding="15,0" Margin="5" p:TextBoxHelper.CornerRadius="15" />
        <StackPanel  Orientation="Horizontal" Grid.Column="0" Grid.ColumnSpan="2" Grid.Row="3" HorizontalAlignment="Center" >
            <Button x:Name="OK" Height="40" p:ButtonHelper.CornerRadius="5" Width="100" Margin="5" Background="#666666" Foreground="WhiteSmoke"  Click="OK_Click">确定</Button>
            <Button x:Name="Cancel" Height="40" p:ButtonHelper.CornerRadius="5" Width="100" Margin="5" Background="OrangeRed" Foreground="WhiteSmoke" Click="Cancel_Click">取消</Button>
        </StackPanel>
    </Grid>
</p:WindowX>
