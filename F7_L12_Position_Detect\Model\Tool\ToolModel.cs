﻿using GalaSoft.MvvmLight;
using HalconDotNet;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Xml.Serialization;

namespace F7_L12_Position_Detect.Model.Tool
{
    public class ToolModel : ObservableObject
    {
        [XmlElement("工具模式")]
        public TestModes TestMode { set; get; } = TestModes.二维码;

        [XmlElement("相机曝光时间")]

        public int ExposureTime { set; get; } = 3000;

        [XmlElement("产品位置序号")]

        public byte ProductPositionNO { set; get; } = 0;

        [XmlElement("相机序列号")]
        public string CameraSerialNumber { set; get; } = ""; // 空值表示使用全局默认相机

        [XmlIgnore]
        public ObservableCollection<string> AvailableCameras { set; get; } = new ObservableCollection<string>();

        [XmlIgnore]
        public int SelectedCameraIndex { set; get; } = -1; // -1表示使用全局默认相机

        [XmlIgnore]
        public QRCode.Reg Reg = new QRCode.Reg();

        [XmlIgnore]
        public PositionDetect.ShapeMatch ShapeMatch = new PositionDetect.ShapeMatch();


        [XmlElement("工具序号")]
        public int Index { set; get; } = 0;

        [XmlElement("触发指令")]
        public string Command { set; get; } = "T1-1";

        [XmlElement("工具名称")]
        public string Name { set; get; } = string.Empty;

        #region 二维码识别

        //[XmlElement("是否识别二维码")]
        //public bool DecodeBarcode { set; get; } = false;

        [XmlElement("二维码内容")]
        public string BarcodeStr { set; get; } = string.Empty;
        [XmlElement("二维码ROI")]
        public ROI BarCodeROI { set; get; } = new ROI();

        [XmlElement("延时")]
        public int Delaytimes { set; get; } = 100;

        [XmlElement("结束码")]

        public string FinishedCode { set; get; } = "REPORTCODE";
        [XmlElement("母排扫码模式")]

        public bool MPMode { set; get; } = true;

        [XmlElement("发送扫码内容")]
        public bool SendBarcode { set; get; } = true;



        #endregion

        #region 位置检测

        //[XmlElement("位置检测")]
        //public bool PositionDetect { set; get; } = true;


        [XmlElement("位置检测模型文件名称")]
        public string ModelFileName { set; get; } = string.Empty;

        [XmlElement("模板匹配分值下限")]

        public double MinScore { set; get; } = 0.8;

        public double ActualScore { set; get; } = 0;


        [XmlElement("比例um/pixel")]
        public double K { set; get; } = 1000;

        [XmlElement("初始X轴坐标")]
        public double InitX { set; get; } = 0;
        [XmlElement("初始Y轴坐标")]
        public double InitY { set; get; } = 0;


        [XmlElement("检测X轴坐标")]
        public double ActualX { set; get; } = 0;
        [XmlElement("检测Y轴坐标")]
        public double ActualY { set; get; } = 0;
        [XmlElement("检测角度")]
        public double ActualAngle { set; get; } = 0;
        [XmlElement("X轴偏差")]
        public double DeltaX { set; get; } = 0;
        [XmlElement("Y轴偏差")]
        public double DeltaY { set; get; } = 0;

        [XmlElement("允许角度偏差")]
        public double AllowAngleDelta { set; get; } = 20;




        //[XmlElement("X轴取反")]
        //public bool DirectX { set; get; } = false;
        //[XmlElement("Y轴取反")]
        //public bool DirectY { set; get; } = false;
        //[XmlElement("XY轴对换")]
        //public bool InvertXY { set; get; } = false;

        [XmlElement("发送结果状态")]
        public bool SendStatus { set; get; } = true;

        //[XmlElement("发送定位数据")]
        //public bool SendPositionData { set; get; } = true;


        //[XmlElement("发送二维码数据")]
        //public bool SendBarcodeData { set; get; } = true;



        //[XmlElement("结果取反")]
        //public bool InvertResult { set; get; } = false;


        [XmlElement("X位置偏差范围")]
        ///单位mm
        public double Allow_X_Delta { set; get; } = 10;
        [XmlElement("Y位置偏差范围")]
        ///单位mm
        public double Allow_Y_Delta { set; get; } = 10;
        //public double Allow_Delta { set; get; } = 10;

        [XmlElement("状态颜色")]
        ///透明,等待
        ///黄色，运行中
        ///绿色，OK
        ///红色, NG
        [XmlIgnore]
        public SolidColorBrush StatusColor
        {

            get
            {
                switch (_ToolStatus)
                {
                    case ToolStatus.识别中:
                        {
                            return Brushes.Orange;
                        }
                    case ToolStatus.OK:
                        {
                            return Brushes.GreenYellow;
                        }
                    case ToolStatus.NG:
                    case ToolStatus.NG2:
                        {
                            return Brushes.OrangeRed;
                        }
                    default:
                        {
                            return new SolidColorBrush(Color.FromArgb(100, 255, 255, 255));
                        }
                }
            }

        }


        private ToolStatus _ToolStatus = ToolStatus.等待中;

        public ToolStatus ToolStatus
        {
            set
            {
                _ToolStatus = value;
                RaisePropertyChanged(() => ToolStatus);
                RaisePropertyChanged(() => StatusColor);

            }
            get { return _ToolStatus; }
        }



        [XmlElement("位置检测ROI")]
        public ROI PositionROI { set; get; } = new ROI();

        #endregion

        #region 面积检测
        [XmlElement("面积ROI")]
        public ROI DimensionROI { set; get; } = new ROI();
        //[XmlElement("面积检测")]
        //public bool DimensionDetect { set; get; } = true;
        [XmlElement("实际面积")]
        public double ActualDimension { set; get; } = 0;
        [XmlElement("最小面积")]
        public double MinDimension { set; get; } = 0;
        [XmlElement("最大面积")]
        public double MaxDimension { set; get; } = 1000000;
        [XmlElement("灰色模式")]
        public bool GrayMode { set; get; } = false;
        [XmlElement("启用红色通道")]
        public bool RedChannelEnabled { set; get; } = true;
        [XmlElement("启用绿色通道")]
        public bool GreenChannelEnabled { set; get; } = true;
        [XmlElement("启用蓝色通道")]
        public bool BlueChannelEnabled { set; get; } = true;
        [XmlElement("最小灰度")]
        public byte MinGray { set; get; } = 128;
        [XmlElement("最大灰度")]
        public byte MaxGray { set; get; } = 255;
        [XmlElement("最小红色通道")]
        public byte MinRed { set; get; } = 128;
        [XmlElement("最大红色通道")]
        public byte MaxRed { set; get; } = 255;
        [XmlElement("最小绿色通道")]
        public byte MinGreen { set; get; } = 128;
        [XmlElement("最大绿色通道")]
        public byte MaxGreen { set; get; } = 255;
        [XmlElement("最小蓝色通道")]
        public byte MinBlue { set; get; } = 128;
        [XmlElement("最大蓝色通道")]
        public byte MaxBlue { set; get; } = 255;
        [XmlElement("过滤最小面积")]
        public int MinAreaFilter { set; get; } = 0;
        [XmlElement("过滤最大面积")]
        public int MaxAreaFilter { set; get; } = int.MaxValue;
        #endregion

        [XmlIgnore]
        public BitmapSource BitmapSource { get; set; } = null;
        [XmlIgnore]
        [XmlElement("实时照片")]
        public BitmapSource CurrentBitmapSource { set; get; } = null;
        [XmlElement("实时照片路径")]
        public string CurrentBitmapFileName { set; get; } = string.Empty;

        public HObject Image = null;

        /// <summary>
        /// 光标位置
        /// </summary>
        [XmlIgnore]
        public Kposition Tposition { set; get; } = new Kposition() { X = 0, Y = 0 };
        [XmlIgnore]
        public KColor TColor { set; get; } = new KColor();


        #region 发送数据
        public string OKCMD { set; get; } = "OK";
        public string NG1CMD { set; get; } = "NG";
        public string NG2CMD { set; get; } = "NG";

        #endregion

    }

    public enum ToolStatus
    {
        等待中,
        识别中,
        OK,
        NG,
        NG2
    }




    public class ROI : ObservableObject
    {
        public int Row1 { set; get; } = 0;
        public int Row2 { set; get; } = 0;
        public int Col1 { set; get; } = 0;
        public int Col2 { set; get; } = 0;
    }
}
