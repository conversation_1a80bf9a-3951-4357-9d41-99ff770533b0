﻿<p:WindowX x:Class="F7_L12_Position_Detect.View.InitPositionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:F7_L12_Position_Detect.View"
        mc:Ignorable="d"
         xmlns:p="clr-namespace:Panuon.WPF.UI;assembly=Panuon.WPF.UI"
        p:WindowXCaption.Background="#666666"
        p:WindowXCaption.Height="45"  
        WindowStartupLocation="CenterScreen"
           p:WindowXCaption.Foreground="WhiteSmoke"
        Title="初始位置设置" Height="450" Width="800">
    <Grid>
        
    </Grid>
</p:WindowX>
